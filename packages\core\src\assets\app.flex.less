/* flex */
.flex_column {
    display: flex;
    flex-direction: column;
}

.flex_row {
    display: flex;
    flex-direction: row;
}

.flex_wrap {
    flex-wrap: wrap;
}

.flex_main_center {
    justify-content: center;
}

.flex_cross_center {
    align-items: center;
}

.flex_row_center {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.flex_column_center {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.flex_center {
    justify-content: center;
    align-items: center;
}

.flex_between {
    justify-content: space-between;
}

.flex_around {
    justify-content: space-around;
}

.flex_end {
    justify-content: flex-end;
}

.flex_start {
    justify-content: flex-start;
}

.flex_cross_end {
    align-self: flex-end;
}

.flex_1 {
    flex: 1;
}

.flex_none {
    flex: none;
}
