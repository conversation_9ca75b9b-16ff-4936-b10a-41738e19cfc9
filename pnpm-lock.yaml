lockfileVersion: 5.4

importers:

  .:
    specifiers:
      '@types/node': ^18.7.18
      '@vitejs/plugin-vue': 3.1.1
      axios: ^0.27.2
      echarts: ^5.3.3
      less: ^4.1.2
      postcss: 8.4.13
      postcss-preset-env: ^7.4.3
      postcss-px-to-viewport: ^1.1.1
      typescript: ^4.6.4
      unplugin-vue-components: ^0.22.4
      unplugin-vue-define-options: ^0.10.0
      vite: 3.1.1
      vite-plugin-compression: 0.5.1
      vite-plugin-style-import: 2.0.0
      vue: ^3.2.37
      vue-tsc: ^0.40.4
      vue3-seamless-scroll: ^2.0.1
    dependencies:
      axios: 0.27.2
      echarts: 5.3.3
      vue: 3.2.39
      vue3-seamless-scroll: 2.0.1
    devDependencies:
      '@types/node': 18.7.23
      '@vitejs/plugin-vue': 3.1.1_vite@3.1.1+vue@3.2.39
      less: 4.1.3
      postcss: 8.4.13
      postcss-preset-env: 7.8.2_postcss@8.4.13
      postcss-px-to-viewport: 1.1.1
      typescript: 4.8.3
      unplugin-vue-components: 0.22.7_vue@3.2.39
      unplugin-vue-define-options: 0.10.0_vue@3.2.39
      vite: 3.1.1_less@4.1.3
      vite-plugin-compression: 0.5.1_vite@3.1.1
      vite-plugin-style-import: 2.0.0_vite@3.1.1
      vue-tsc: 0.40.13_typescript@4.8.3

  packages/core:
    specifiers:
      '@types/lodash': ^4.14.186
      dayjs: ^1.11.5
      lodash: ^4.17.21
    dependencies:
      dayjs: registry.npmmirror.com/dayjs/1.11.5
      lodash: registry.npmmirror.com/lodash/4.17.21
    devDependencies:
      '@types/lodash': registry.npmmirror.com/@types/lodash/4.14.186

  projects/demo:
    specifiers:
      '@ztstory/datav-core': workspace:^1.0.0
    dependencies:
      '@ztstory/datav-core': link:../../packages/core

packages:

  /@antfu/utils/0.5.2:
    resolution: {integrity: sha512-CQkeV+oJxUazwjlHD0/3ZD08QWKuGQkhnrKo3e6ly5pd48VUpXbb77q0xMU4+vc2CkJnDS02Eq/M9ugyX20XZA==, registry: https://registry.npm.taobao.org/}
    dev: true

  /@babel/helper-string-parser/7.18.10:
    resolution: {integrity: sha512-XtIfWmeNY3i4t7t4D2t02q50HvqHybPqW2ki1kosnvWCwuCMeo81Jf0gwr85jy/neUdg5XDdeFE/80DXiO+njw==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-identifier/7.19.1:
    resolution: {integrity: sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=6.9.0'}

  /@babel/parser/7.19.1:
    resolution: {integrity: sha512-h7RCSorm1DdTVGJf3P2Mhj3kdnkmF/EiysUkzS2TdgAYqyjFdMQJbVuXOBej2SBJaXan/lIVtT6KkGbyyq753A==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.19.0

  /@babel/types/7.19.0:
    resolution: {integrity: sha512-YuGopBq3ke25BVSiS6fgF49Ul9gH1x70Bcr6bqRLjWCkcX8Hre1/5+z+IiWOIerRMSSEfGZVB9z9kyq7wVs9YA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.18.10
      '@babel/helper-validator-identifier': 7.19.1
      to-fast-properties: 2.0.0

  /@csstools/postcss-cascade-layers/1.1.1_postcss@8.4.13:
    resolution: {integrity: sha512-+KdYrpKC5TgomQr2DlZF4lDEpHcoxnj5IGddYYfBWJAKfj1JtuHUIqMa+E1pJJ+z3kvDViWMqyqPlG4Ja7amQA==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/selector-specificity': 2.0.2_qiplrb533afbljv7sbepwo7yse
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /@csstools/postcss-color-function/1.1.1_postcss@8.4.13:
    resolution: {integrity: sha512-Bc0f62WmHdtRDjf5f3e2STwRAl89N2CLb+9iAwzrv4L2hncrbDwnQD9PCq0gtAt7pOI2leIV08HIBUd4jxD8cw==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0_postcss@8.4.13
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-font-format-keywords/1.0.1_postcss@8.4.13:
    resolution: {integrity: sha512-ZgrlzuUAjXIOc2JueK0X5sZDjCtgimVp/O5CEqTcs5ShWBa6smhWYbS0x5cVc/+rycTDbjjzoP0KTDnUneZGOg==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-hwb-function/1.0.2_postcss@8.4.13:
    resolution: {integrity: sha512-YHdEru4o3Rsbjmu6vHy4UKOXZD+Rn2zmkAmLRfPet6+Jz4Ojw8cbWxe1n42VaXQhD3CQUXXTooIy8OkVbUcL+w==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-ic-unit/1.0.1_postcss@8.4.13:
    resolution: {integrity: sha512-Ot1rcwRAaRHNKC9tAqoqNZhjdYBzKk1POgWfhN4uCOE47ebGcLRqXjKkApVDpjifL6u2/55ekkpnFcp+s/OZUw==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0_postcss@8.4.13
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-is-pseudo-class/2.0.7_postcss@8.4.13:
    resolution: {integrity: sha512-7JPeVVZHd+jxYdULl87lvjgvWldYu+Bc62s9vD/ED6/QTGjy0jy0US/f6BG53sVMTBJ1lzKZFpYmofBN9eaRiA==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/selector-specificity': 2.0.2_qiplrb533afbljv7sbepwo7yse
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /@csstools/postcss-nested-calc/1.0.0_postcss@8.4.13:
    resolution: {integrity: sha512-JCsQsw1wjYwv1bJmgjKSoZNvf7R6+wuHDAbi5f/7MbFhl2d/+v+TvBTU4BJH3G1X1H87dHl0mh6TfYogbT/dJQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-normalize-display-values/1.0.1_postcss@8.4.13:
    resolution: {integrity: sha512-jcOanIbv55OFKQ3sYeFD/T0Ti7AMXc9nM1hZWu8m/2722gOTxFg7xYu4RDLJLeZmPUVQlGzo4jhzvTUq3x4ZUw==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-oklab-function/1.1.1_postcss@8.4.13:
    resolution: {integrity: sha512-nJpJgsdA3dA9y5pgyb/UfEzE7W5Ka7u0CX0/HIMVBNWzWemdcTH3XwANECU6anWv/ao4vVNLTMxhiPNZsTK6iA==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0_postcss@8.4.13
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-progressive-custom-properties/1.3.0_postcss@8.4.13:
    resolution: {integrity: sha512-ASA9W1aIy5ygskZYuWams4BzafD12ULvSypmaLJT2jvQ8G0M3I8PRQhC0h7mG0Z3LI05+agZjqSR9+K9yaQQjA==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-stepped-value-functions/1.0.1_postcss@8.4.13:
    resolution: {integrity: sha512-dz0LNoo3ijpTOQqEJLY8nyaapl6umbmDcgj4AD0lgVQ572b2eqA1iGZYTTWhrcrHztWDDRAX2DGYyw2VBjvCvQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-text-decoration-shorthand/1.0.0_postcss@8.4.13:
    resolution: {integrity: sha512-c1XwKJ2eMIWrzQenN0XbcfzckOLLJiczqy+YvfGmzoVXd7pT9FfObiSEfzs84bpE/VqfpEuAZ9tCRbZkZxxbdw==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-trigonometric-functions/1.0.2_postcss@8.4.13:
    resolution: {integrity: sha512-woKaLO///4bb+zZC2s80l+7cm07M7268MsyG3M0ActXXEFi6SuhvriQYcb58iiKGbjwwIU7n45iRLEHypB47Og==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /@csstools/postcss-unset-value/1.0.2_postcss@8.4.13:
    resolution: {integrity: sha512-c8J4roPBILnelAsdLr4XOAR/GsTm0GJi4XpcfvoWk3U6KiTCqiFYc63KhRMQQX35jYMp4Ao8Ij9+IZRgMfJp1g==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
    dev: true

  /@csstools/selector-specificity/2.0.2_qiplrb533afbljv7sbepwo7yse:
    resolution: {integrity: sha512-IkpVW/ehM1hWKln4fCA3NzJU8KwD+kIOvPZA4cqxoJHtE21CCzjyp+Kxbu0i5I4tBNOlXPL9mjwnWlL0VEG4Fg==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
      postcss-selector-parser: ^6.0.10
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /@esbuild/android-arm/0.15.9:
    resolution: {integrity: sha512-VZPy/ETF3fBG5PiinIkA0W/tlsvlEgJccyN2DzWZEl0DlVKRbu91PvY2D6Lxgluj4w9QtYHjOWjAT44C+oQ+EQ==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64/0.15.9:
    resolution: {integrity: sha512-O+NfmkfRrb3uSsTa4jE3WApidSe3N5++fyOVGP1SmMZi4A3BZELkhUUvj5hwmMuNdlpzAZ8iAPz2vmcR7DCFQA==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.13.0
    dev: true

  /@rollup/pluginutils/4.2.1:
    resolution: {integrity: sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==}
    engines: {node: '>= 8.0.0'}
    dependencies:
      estree-walker: 2.0.2
      picomatch: 2.3.1
    dev: true

  /@types/node/18.7.23:
    resolution: {integrity: sha512-DWNcCHolDq0ZKGizjx2DZjR/PqsYwAcYUJmfMWqtVU2MBMG5Mo+xFZrhGId5r/O5HOuMPyQEcM6KUBp5lBZZBg==, registry: https://registry.npm.taobao.org/}
    dev: true

  /@vitejs/plugin-vue/3.1.1_vite@3.1.1+vue@3.2.39:
    resolution: {integrity: sha512-fr2F2eRQVVvbnBqzXotQ99y42QUSjAFrSe3Z8T+R8KhWcme+W46eqldZUhT1kafvw7eV/hlwDb1HUvOdprJNxw==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^3.0.0
      vue: ^3.2.25
    dependencies:
      vite: 3.1.1_less@4.1.3
      vue: 3.2.39
    dev: true

  /@volar/code-gen/0.40.13:
    resolution: {integrity: sha512-4gShBWuMce868OVvgyA1cU5WxHbjfEme18Tw6uVMfweZCF5fB2KECG0iPrA9D54vHk3FeHarODNwgIaaFfUBlA==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@volar/source-map': 0.40.13
    dev: true

  /@volar/source-map/0.40.13:
    resolution: {integrity: sha512-dbdkAB2Nxb0wLjAY5O64o3ywVWlAGONnBIoKAkXSf6qkGZM+nJxcizsoiI66K+RHQG0XqlyvjDizfnTxr+6PWg==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@vue/reactivity': 3.2.38
    dev: true

  /@volar/typescript-faster/0.40.13:
    resolution: {integrity: sha512-uy+TlcFkKoNlKEnxA4x5acxdxLyVDIXGSc8cYDNXpPKjBKXrQaetzCzlO3kVBqu1VLMxKNGJMTKn35mo+ILQmw==, registry: https://registry.npm.taobao.org/}
    dependencies:
      semver: 7.3.7
    dev: true

  /@volar/vue-language-core/0.40.13:
    resolution: {integrity: sha512-QkCb8msi2KUitTdM6Y4kAb7/ZlEvuLcbBFOC2PLBlFuoZwyxvSP7c/dBGmKGtJlEvMX0LdCyrg5V2aBYxD38/Q==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@volar/code-gen': 0.40.13
      '@volar/source-map': 0.40.13
      '@vue/compiler-core': 3.2.39
      '@vue/compiler-dom': 3.2.39
      '@vue/compiler-sfc': 3.2.39
      '@vue/reactivity': 3.2.39
      '@vue/shared': 3.2.39
    dev: true

  /@volar/vue-typescript/0.40.13:
    resolution: {integrity: sha512-o7bNztwjs8JmbQjVkrnbZUOfm7q4B8ZYssETISN1tRaBdun6cfNqgpkvDYd+VUBh1O4CdksvN+5BUNnwAz4oCQ==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@volar/code-gen': 0.40.13
      '@volar/typescript-faster': 0.40.13
      '@volar/vue-language-core': 0.40.13
    dev: true

  /@vue-macros/common/0.10.0_vue@3.2.39:
    resolution: {integrity: sha512-g5sTVG0ltL1f8EjEO8W5EG+jEKjircYprlkr1Qc+pNvqCIRV+OlAi/WHNrMEK6PIwb2t3EWudCFMo5Fq/IMGMw==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=14.19.0'}
    peerDependencies:
      vue: ^2.7.0 || ^3.2.25
    dependencies:
      '@babel/types': 7.19.0
      '@vue/compiler-sfc': 3.2.39
      magic-string: 0.26.4
      vue: 3.2.39
    dev: true

  /@vue/compiler-core/3.2.39:
    resolution: {integrity: sha512-mf/36OWXqWn0wsC40nwRRGheR/qoID+lZXbIuLnr4/AngM0ov8Xvv8GHunC0rKRIkh60bTqydlqTeBo49rlbqw==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@babel/parser': 7.19.1
      '@vue/shared': 3.2.39
      estree-walker: 2.0.2
      source-map: 0.6.1

  /@vue/compiler-dom/3.2.39:
    resolution: {integrity: sha512-HMFI25Be1C8vLEEv1hgEO1dWwG9QQ8LTTPmCkblVJY/O3OvWx6r1+zsox5mKPMGvqYEZa6l8j+xgOfUspgo7hw==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@vue/compiler-core': 3.2.39
      '@vue/shared': 3.2.39

  /@vue/compiler-sfc/3.2.39:
    resolution: {integrity: sha512-fqAQgFs1/BxTUZkd0Vakn3teKUt//J3c420BgnYgEOoVdTwYpBTSXCMJ88GOBCylmUBbtquGPli9tVs7LzsWIA==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@babel/parser': 7.19.1
      '@vue/compiler-core': 3.2.39
      '@vue/compiler-dom': 3.2.39
      '@vue/compiler-ssr': 3.2.39
      '@vue/reactivity-transform': 3.2.39
      '@vue/shared': 3.2.39
      estree-walker: 2.0.2
      magic-string: 0.25.9
      postcss: 8.4.16
      source-map: 0.6.1

  /@vue/compiler-ssr/3.2.39:
    resolution: {integrity: sha512-EoGCJ6lincKOZGW+0Ky4WOKsSmqL7hp1ZYgen8M7u/mlvvEQUaO9tKKOy7K43M9U2aA3tPv0TuYYQFrEbK2eFQ==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@vue/compiler-dom': 3.2.39
      '@vue/shared': 3.2.39

  /@vue/reactivity-transform/3.2.39:
    resolution: {integrity: sha512-HGuWu864zStiWs9wBC6JYOP1E00UjMdDWIG5W+FpUx28hV3uz9ODOKVNm/vdOy/Pvzg8+OcANxAVC85WFBbl3A==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@babel/parser': 7.19.1
      '@vue/compiler-core': 3.2.39
      '@vue/shared': 3.2.39
      estree-walker: 2.0.2
      magic-string: 0.25.9

  /@vue/reactivity/3.2.38:
    resolution: {integrity: sha512-6L4myYcH9HG2M25co7/BSo0skKFHpAN8PhkNPM4xRVkyGl1K5M3Jx4rp5bsYhvYze2K4+l+pioN4e6ZwFLUVtw==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@vue/shared': 3.2.38
    dev: true

  /@vue/reactivity/3.2.39:
    resolution: {integrity: sha512-vlaYX2a3qMhIZfrw3Mtfd+BuU+TZmvDrPMa+6lpfzS9k/LnGxkSuf0fhkP0rMGfiOHPtyKoU9OJJJFGm92beVQ==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@vue/shared': 3.2.39

  /@vue/runtime-core/3.2.39:
    resolution: {integrity: sha512-xKH5XP57JW5JW+8ZG1khBbuLakINTgPuINKL01hStWLTTGFOrM49UfCFXBcFvWmSbci3gmJyLl2EAzCaZWsx8g==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@vue/reactivity': 3.2.39
      '@vue/shared': 3.2.39

  /@vue/runtime-dom/3.2.39:
    resolution: {integrity: sha512-4G9AEJP+sLhsqf5wXcyKVWQKUhI+iWfy0hWQgea+CpaTD7BR0KdQzvoQdZhwCY6B3oleSyNLkLAQwm0ya/wNoA==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@vue/runtime-core': 3.2.39
      '@vue/shared': 3.2.39
      csstype: 2.6.21

  /@vue/server-renderer/3.2.39_vue@3.2.39:
    resolution: {integrity: sha512-1yn9u2YBQWIgytFMjz4f/t0j43awKytTGVptfd3FtBk76t1pd8mxbek0G/DrnjJhd2V7mSTb5qgnxMYt8Z5iSQ==, registry: https://registry.npm.taobao.org/}
    peerDependencies:
      vue: 3.2.39
    dependencies:
      '@vue/compiler-ssr': 3.2.39
      '@vue/shared': 3.2.39
      vue: 3.2.39

  /@vue/shared/3.2.38:
    resolution: {integrity: sha512-dTyhTIRmGXBjxJE+skC8tTWCGLCVc4wQgRRLt8+O9p5ewBAjoBwtCAkLPrtToSr1xltoe3st21Pv953aOZ7alg==, registry: https://registry.npm.taobao.org/}
    dev: true

  /@vue/shared/3.2.39:
    resolution: {integrity: sha512-D3dl2ZB9qE6mTuWPk9RlhDeP1dgNRUKC3NJxji74A4yL8M2MwlhLKUC/49WHjrNzSPug58fWx/yFbaTzGAQSBw==, registry: https://registry.npm.taobao.org/}

  /acorn/8.8.0:
    resolution: {integrity: sha512-QOxyigPVrpZ2GXT+PFyZTl6TtOFc5egxHIP9IlQ+RbupQuX4RkT/Bee4/kQuC02Xkzg84JcT7oLYtDIQxp+v7w==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: true

  /anymatch/3.1.2:
    resolution: {integrity: sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    dev: true

  /ast-walker-scope/0.2.3:
    resolution: {integrity: sha512-9reB+iYF6jCCDqKDNNQI8iA2MJcy0jCLvEjfya72F7Zai5i2CB8hk9K/kzkZhagja9othQCFPEvQW11LhPKjmg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=14.19.0'}
    dependencies:
      '@babel/parser': 7.19.1
      '@babel/types': 7.19.0
    dev: true

  /asynckit/0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==, registry: https://registry.npm.taobao.org/}
    dev: false

  /autoprefixer/10.4.12_postcss@8.4.13:
    resolution: {integrity: sha512-WrCGV9/b97Pa+jtwf5UGaRjgQIg7OK3D06GnoYoZNcG1Xb8Gt3EfuKjlhh9i/VtT16g6PYjZ69jdJ2g8FxSC4Q==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: 4.21.4
      caniuse-lite: 1.0.30001412
      fraction.js: 4.2.0
      normalize-range: 0.1.2
      picocolors: 1.0.0
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /axios/0.27.2:
    resolution: {integrity: sha512-t+yRIyySRTp/wua5xEr+z1q60QmLq8ABsS5O9Me1AsE5dfKqgnCFzwiCZZ/cGNd1lq4/7akDWMxdhVlucjmnOQ==, registry: https://registry.npm.taobao.org/}
    dependencies:
      follow-redirects: 1.15.2
      form-data: 4.0.0
    transitivePeerDependencies:
      - debug
    dev: false

  /balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==, registry: https://registry.npm.taobao.org/}
    dev: true

  /binary-extensions/2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=8'}
    dev: true

  /brace-expansion/2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==, registry: https://registry.npm.taobao.org/}
    dependencies:
      balanced-match: 1.0.2
    dev: true

  /braces/3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1
    dev: true

  /browserslist/4.21.4:
    resolution: {integrity: sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001412
      electron-to-chromium: 1.4.264
      node-releases: 2.0.6
      update-browserslist-db: 1.0.9_browserslist@4.21.4
    dev: true

  /camel-case/4.1.2:
    resolution: {integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==}
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.4.0
    dev: true

  /caniuse-lite/1.0.30001412:
    resolution: {integrity: sha512-+TeEIee1gS5bYOiuf+PS/kp2mrXic37Hl66VY6EAfxasIk5fELTktK2oOezYed12H8w7jt3s512PpulQidPjwA==, registry: https://registry.npm.taobao.org/}
    dev: true

  /capital-case/1.0.4:
    resolution: {integrity: sha512-ds37W8CytHgwnhGGTi88pcPyR15qoNkOpYwmMMfnWqqWgESapLqvDx6huFjQ5vqWSn2Z06173XNA7LtMOeUh1A==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.4.0
      upper-case-first: 2.0.2
    dev: true

  /chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /change-case/4.1.2:
    resolution: {integrity: sha512-bSxY2ws9OtviILG1EiY5K7NNxkqg/JnRnFxLtKQ96JaviiIxi7djMrSd0ECT9AC+lttClmYwKw53BWpOMblo7A==}
    dependencies:
      camel-case: 4.1.2
      capital-case: 1.0.4
      constant-case: 3.0.4
      dot-case: 3.0.4
      header-case: 2.0.4
      no-case: 3.0.4
      param-case: 3.0.4
      pascal-case: 3.1.2
      path-case: 3.0.4
      sentence-case: 3.0.4
      snake-case: 3.0.4
      tslib: 2.4.0
    dev: true

  /chokidar/3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.2
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.2
    dev: true

  /color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}
    dev: true

  /combined-stream/1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: false

  /console/0.7.2:
    resolution: {integrity: sha512-+JSDwGunA4MTEgAV/4VBKwUHonP8CzJ/6GIuwPi6acKFqFfHUdSGCm89ZxZ5FfGWdZfkdgAroy5bJ5FSeN/t4g==}
    dev: true

  /constant-case/3.0.4:
    resolution: {integrity: sha512-I2hSBi7Vvs7BEuJDr5dDHfzb/Ruj3FyvFyh7KLilAjNQw3Be+xgqUBA2W6scVEcL0hL1dwPRtIqEPVUCKkSsyQ==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.4.0
      upper-case: 2.0.2
    dev: true

  /copy-anything/2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==, registry: https://registry.npm.taobao.org/}
    dependencies:
      is-what: 3.14.1
    dev: true

  /css-blank-pseudo/3.0.3_postcss@8.4.13:
    resolution: {integrity: sha512-VS90XWtsHGqoM0t4KpH053c4ehxZ2E6HtGI7x68YFV0pTo/QmkV/YFA+NnlvK8guxZVNWGQhVNJGC39Q8XF4OQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    hasBin: true
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /css-has-pseudo/3.0.4_postcss@8.4.13:
    resolution: {integrity: sha512-Vse0xpR1K9MNlp2j5w1pgWIJtm1a8qS0JwS9goFYcImjlHEmywP9VUF05aGBXzGpDJF86QXk4L0ypBmwPhGArw==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    hasBin: true
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /css-prefers-color-scheme/6.0.3_postcss@8.4.13:
    resolution: {integrity: sha512-4BqMbZksRkJQx2zAjrokiGMd07RqOa2IxIrrN10lyBe9xhn9DEvjUK79J6jkeiv9D9hQFXKb6g1jwU62jziJZA==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    hasBin: true
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.13
    dev: true

  /cssdb/7.0.1:
    resolution: {integrity: sha512-pT3nzyGM78poCKLAEy2zWIVX2hikq6dIrjuZzLV98MumBg+xMTNYfHx7paUlfiRTgg91O/vR889CIf+qiv79Rw==, registry: https://registry.npm.taobao.org/}
    dev: true

  /cssesc/3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /csstype/2.6.21:
    resolution: {integrity: sha512-Z1PhmomIfypOpoMjRQB70jfvy/wxT50qW08YXO5lMIJkrdq4yOTR+AW7FqutScmB9NkLwxo+jU+kZLbofZZq/w==, registry: https://registry.npm.taobao.org/}

  /debug/3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: true
    optional: true

  /debug/4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /delayed-stream/1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.4.0'}
    dev: false

  /dot-case/3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.4.0
    dev: true

  /echarts/5.3.3:
    resolution: {integrity: sha512-BRw2serInRwO5SIwRviZ6Xgm5Lb7irgz+sLiFMmy/HOaf4SQ+7oYqxKzRHAKp4xHQ05AuHw1xvoQWJjDQq/FGw==, registry: https://registry.npm.taobao.org/}
    dependencies:
      tslib: 2.3.0
      zrender: 5.3.2
    dev: false

  /electron-to-chromium/1.4.264:
    resolution: {integrity: sha512-AZ6ZRkucHOQT8wke50MktxtmcWZr67kE17X/nAXFf62NIdMdgY6xfsaJD5Szoy84lnkuPWH+4tTNE3s2+bPCiw==, registry: https://registry.npm.taobao.org/}
    dev: true

  /errno/0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true
    requiresBuild: true
    dependencies:
      prr: 1.0.1
    dev: true
    optional: true

  /es-module-lexer/0.9.3:
    resolution: {integrity: sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==}
    dev: true

  /esbuild-android-64/0.15.9:
    resolution: {integrity: sha512-HQCX7FJn9T4kxZQkhPjNZC7tBWZqJvhlLHPU2SFzrQB/7nDXjmTIFpFTjt7Bd1uFpeXmuwf5h5fZm+x/hLnhbw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-android-arm64/0.15.9:
    resolution: {integrity: sha512-E6zbLfqbFVCNEKircSHnPiSTsm3fCRxeIMPfrkS33tFjIAoXtwegQfVZqMGR0FlsvVxp2NEDOUz+WW48COCjSg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-darwin-64/0.15.9:
    resolution: {integrity: sha512-gI7dClcDN/HHVacZhTmGjl0/TWZcGuKJ0I7/xDGJwRQQn7aafZGtvagOFNmuOq+OBFPhlPv1T6JElOXb0unkSQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-darwin-arm64/0.15.9:
    resolution: {integrity: sha512-VZIMlcRN29yg/sv7DsDwN+OeufCcoTNaTl3Vnav7dL/nvsApD7uvhVRbgyMzv0zU/PP0xRhhIpTyc7lxEzHGSw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-freebsd-64/0.15.9:
    resolution: {integrity: sha512-uM4z5bTvuAXqPxrI204txhlsPIolQPWRMLenvGuCPZTnnGlCMF2QLs0Plcm26gcskhxewYo9LkkmYSS5Czrb5A==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-freebsd-arm64/0.15.9:
    resolution: {integrity: sha512-HHDjT3O5gWzicGdgJ5yokZVN9K9KG05SnERwl9nBYZaCjcCgj/sX8Ps1jvoFSfNCO04JSsHSOWo4qvxFuj8FoA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-32/0.15.9:
    resolution: {integrity: sha512-AQIdE8FugGt1DkcekKi5ycI46QZpGJ/wqcMr7w6YUmOmp2ohQ8eO4sKUsOxNOvYL7hGEVwkndSyszR6HpVHLFg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-64/0.15.9:
    resolution: {integrity: sha512-4RXjae7g6Qs7StZyiYyXTZXBlfODhb1aBVAjd+ANuPmMhWthQilWo7rFHwJwL7DQu1Fjej2sODAVwLbcIVsAYQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-arm/0.15.9:
    resolution: {integrity: sha512-3Zf2GVGUOI7XwChH3qrnTOSqfV1V4CAc/7zLVm4lO6JT6wbJrTgEYCCiNSzziSju+J9Jhf9YGWk/26quWPC6yQ==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-arm64/0.15.9:
    resolution: {integrity: sha512-a+bTtxJmYmk9d+s2W4/R1SYKDDAldOKmWjWP0BnrWtDbvUBNOm++du0ysPju4mZVoEFgS1yLNW+VXnG/4FNwdQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-mips64le/0.15.9:
    resolution: {integrity: sha512-Zn9HSylDp89y+TRREMDoGrc3Z4Hs5u56ozZLQCiZAUx2+HdbbXbWdjmw3FdTJ/i7t5Cew6/Q+6kfO3KCcFGlyw==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-ppc64le/0.15.9:
    resolution: {integrity: sha512-OEiOxNAMH9ENFYqRsWUj3CWyN3V8P3ZXyfNAtX5rlCEC/ERXrCEFCJji/1F6POzsXAzxvUJrTSTCy7G6BhA6Fw==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-riscv64/0.15.9:
    resolution: {integrity: sha512-ukm4KsC3QRausEFjzTsOZ/qqazw0YvJsKmfoZZm9QW27OHjk2XKSQGGvx8gIEswft/Sadp03/VZvAaqv5AIwNA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-s390x/0.15.9:
    resolution: {integrity: sha512-uDOQEH55wQ6ahcIKzQr3VyjGc6Po/xblLGLoUk3fVL1qjlZAibtQr6XRfy5wPJLu/M2o0vQKLq4lyJ2r1tWKcw==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-netbsd-64/0.15.9:
    resolution: {integrity: sha512-yWgxaYTQz+TqX80wXRq6xAtb7GSBAp6gqLKfOdANg9qEmAI1Bxn04IrQr0Mzm4AhxvGKoHzjHjMgXbCCSSDxcw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-openbsd-64/0.15.9:
    resolution: {integrity: sha512-JmS18acQl4iSAjrEha1MfEmUMN4FcnnrtTaJ7Qg0tDCOcgpPPQRLGsZqhes0vmx8VA6IqRyScqXvaL7+Q0Uf3A==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-sunos-64/0.15.9:
    resolution: {integrity: sha512-UKynGSWpzkPmXW3D2UMOD9BZPIuRaSqphxSCwScfEE05Be3KAmvjsBhht1fLzKpiFVJb0BYMd4jEbWMyJ/z1hQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-windows-32/0.15.9:
    resolution: {integrity: sha512-aqXvu4/W9XyTVqO/hw3rNxKE1TcZiEYHPsXM9LwYmKSX9/hjvfIJzXwQBlPcJ/QOxedfoMVH0YnhhQ9Ffb0RGA==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-windows-64/0.15.9:
    resolution: {integrity: sha512-zm7h91WUmlS4idMtjvCrEeNhlH7+TNOmqw5dJPJZrgFaxoFyqYG6CKDpdFCQXdyKpD5yvzaQBOMVTCBVKGZDEg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-windows-arm64/0.15.9:
    resolution: {integrity: sha512-yQEVIv27oauAtvtuhJVfSNMztJJX47ismRS6Sv2QMVV9RM+6xjbMWuuwM2nxr5A2/gj/mu2z9YlQxiwoFRCfZA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild/0.15.9:
    resolution: {integrity: sha512-OnYr1rkMVxtmMHIAKZLMcEUlJmqcbxBz9QoBU8G9v455na0fuzlT/GLu6l+SRghrk0Mm2fSSciMmzV43Q8e0Gg==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/android-arm': 0.15.9
      '@esbuild/linux-loong64': 0.15.9
      esbuild-android-64: 0.15.9
      esbuild-android-arm64: 0.15.9
      esbuild-darwin-64: 0.15.9
      esbuild-darwin-arm64: 0.15.9
      esbuild-freebsd-64: 0.15.9
      esbuild-freebsd-arm64: 0.15.9
      esbuild-linux-32: 0.15.9
      esbuild-linux-64: 0.15.9
      esbuild-linux-arm: 0.15.9
      esbuild-linux-arm64: 0.15.9
      esbuild-linux-mips64le: 0.15.9
      esbuild-linux-ppc64le: 0.15.9
      esbuild-linux-riscv64: 0.15.9
      esbuild-linux-s390x: 0.15.9
      esbuild-netbsd-64: 0.15.9
      esbuild-openbsd-64: 0.15.9
      esbuild-sunos-64: 0.15.9
      esbuild-windows-32: 0.15.9
      esbuild-windows-64: 0.15.9
      esbuild-windows-arm64: 0.15.9
    dev: true

  /escalade/3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=6'}
    dev: true

  /estree-walker/2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==, registry: https://registry.npm.taobao.org/}

  /fast-glob/3.2.12:
    resolution: {integrity: sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5
    dev: true

  /fastq/1.13.0:
    resolution: {integrity: sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==, registry: https://registry.npm.taobao.org/}
    dependencies:
      reusify: 1.0.4
    dev: true

  /fill-range/7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: true

  /follow-redirects/1.15.2:
    resolution: {integrity: sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: false

  /form-data/4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 6'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: false

  /fraction.js/4.2.0:
    resolution: {integrity: sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA==, registry: https://registry.npm.taobao.org/}
    dev: true

  /fs-extra/10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}
    dependencies:
      graceful-fs: 4.2.10
      jsonfile: 6.1.0
      universalify: 2.0.0
    dev: true

  /fsevents/2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /function-bind/1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}
    dev: true

  /glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /graceful-fs/4.2.10:
    resolution: {integrity: sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==}
    dev: true

  /has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}
    dev: true

  /has/1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1
    dev: true

  /header-case/2.0.4:
    resolution: {integrity: sha512-H/vuk5TEEVZwrR0lp2zed9OCo1uAILMlx0JEMgC26rzyJJ3N1v6XkwHHXJQdR2doSjcGPM6OKPYoJgf0plJ11Q==}
    dependencies:
      capital-case: 1.0.4
      tslib: 2.4.0
    dev: true

  /iconv-lite/0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: true
    optional: true

  /image-size/0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  /is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0
    dev: true

  /is-core-module/2.10.0:
    resolution: {integrity: sha512-Erxj2n/LDAZ7H8WNJXd9tw38GYM3dv8rk8Zcs+jJuxYTW7sozH+SS8NtrSjVL1/vpLvWi1hxy96IzjJ3EHTJJg==}
    dependencies:
      has: 1.0.3
    dev: true

  /is-extglob/2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.12.0'}
    dev: true

  /is-what/3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==, registry: https://registry.npm.taobao.org/}
    dev: true

  /jsonfile/6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}
    dependencies:
      universalify: 2.0.0
    optionalDependencies:
      graceful-fs: 4.2.10
    dev: true

  /less/4.1.3:
    resolution: {integrity: sha512-w16Xk/Ta9Hhyei0Gpz9m7VS8F28nieJaL/VyShID7cYvP6IL5oHeL6p4TXSDJqZE/lNv0oJ2pGVjJsRkfwm5FA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.4.0
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.10
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.1.0
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /local-pkg/0.4.2:
    resolution: {integrity: sha512-mlERgSPrbxU3BP4qBqAvvwlgW4MTg78iwJdGGnv7kibKjWcJksrG3t6LB5lXI93wXRDvG4NpUgJFmTG4T6rdrg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=14'}
    dev: true

  /lower-case/2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}
    dependencies:
      tslib: 2.4.0
    dev: true

  /lru-cache/6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /magic-string/0.25.9:
    resolution: {integrity: sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==}
    dependencies:
      sourcemap-codec: 1.4.8

  /magic-string/0.26.4:
    resolution: {integrity: sha512-e5uXtVJ22aEpK9u1+eQf0fSxHeqwyV19K+uGnlROCxUhzwRip9tBsaMViK/0vC3viyPd5Gtucp3UmEp/Q2cPTQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=12'}
    dependencies:
      sourcemap-codec: 1.4.8
    dev: true

  /make-dir/2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}
    requiresBuild: true
    dependencies:
      pify: 4.0.1
      semver: 5.7.1
    dev: true
    optional: true

  /merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 8'}
    dev: true

  /micromatch/4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1
    dev: true

  /mime-db/1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-types/2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: false

  /mime/1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  /minimatch/5.1.0:
    resolution: {integrity: sha512-9TPBGGak4nHfGZsPBohm9AWg6NoT7QTCehS3BIJABslyZbzxfV78QM2Y6+i741OPZIafFAaiiEMh5OyIrJPgtg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=10'}
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /ms/2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: true

  /ms/2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}
    dev: true
    optional: true

  /nanoid/3.3.4:
    resolution: {integrity: sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  /needle/3.1.0:
    resolution: {integrity: sha512-gCE9weDhjVGCRqS8dwDR/D3GTAeyXLXuqp7I8EzH6DllZGXSUyxuqqLh+YX9rMAWaaTFyVAg6rHGL25dqvczKw==}
    engines: {node: '>= 4.4.x'}
    hasBin: true
    requiresBuild: true
    dependencies:
      debug: 3.2.7
      iconv-lite: 0.6.3
      sax: 1.2.4
    transitivePeerDependencies:
      - supports-color
    dev: true
    optional: true

  /no-case/3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}
    dependencies:
      lower-case: 2.0.2
      tslib: 2.4.0
    dev: true

  /node-releases/2.0.6:
    resolution: {integrity: sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==, registry: https://registry.npm.taobao.org/}
    dev: true

  /normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.10.0'}
    dev: true

  /normalize-range/0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.10.0'}
    dev: true

  /object-assign/4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.10.0'}
    dev: true

  /param-case/3.0.4:
    resolution: {integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.4.0
    dev: true

  /parse-node-version/1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>= 0.10'}
    dev: true

  /pascal-case/3.1.2:
    resolution: {integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.4.0
    dev: true

  /path-case/3.0.4:
    resolution: {integrity: sha512-qO4qCFjXqVTrcbPt/hQfhTQ+VhFsqNKOPtytgNKkKxSoEp3XPUQ8ObFuePylOIok5gjn69ry8XiULxCwot3Wfg==}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.4.0
    dev: true

  /path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}
    dev: true

  /pathe/0.2.0:
    resolution: {integrity: sha512-sTitTPYnn23esFR3RlqYBWn4c45WGeLcsKzQiUpXJAyfcWkolvlYpV8FLo7JishK946oQwMFUCHXQ9AjGPKExw==}
    dev: true

  /picocolors/1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==, registry: https://registry.npm.taobao.org/}

  /picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=8.6'}
    dev: true

  /pify/4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=6'}
    dev: true
    optional: true

  /postcss-attribute-case-insensitive/5.0.2_postcss@8.4.13:
    resolution: {integrity: sha512-XIidXV8fDr0kKt28vqki84fRK8VW8eTuIa4PChv2MqKuT6C9UjmSKzen6KaWhWEoYvwxFCa7n/tC1SZ3tyq4SQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /postcss-clamp/4.1.0_postcss@8.4.13:
    resolution: {integrity: sha512-ry4b1Llo/9zz+PKC+030KUnPITTJAHeOwjfAyyB60eT0AorGLdzp52s31OsPRHRf8NchkgFoG2y6fCfn1IV1Ow==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=7.6.0'}
    peerDependencies:
      postcss: ^8.4.6
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-color-functional-notation/4.2.4_postcss@8.4.13:
    resolution: {integrity: sha512-2yrTAUZUab9s6CpxkxC4rVgFEVaR6/2Pipvi6qcgvnYiVqZcbDHEoBDhrXzyb7Efh2CCfHQNtcqWcIruDTIUeg==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-color-hex-alpha/8.0.4_postcss@8.4.13:
    resolution: {integrity: sha512-nLo2DCRC9eE4w2JmuKgVA3fGL3d01kGq752pVALF68qpGLmx2Qrk91QTKkdUqqp45T1K1XV8IhQpcu1hoAQflQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-color-rebeccapurple/7.1.1_postcss@8.4.13:
    resolution: {integrity: sha512-pGxkuVEInwLHgkNxUc4sdg4g3py7zUeCQ9sMfwyHAT+Ezk8a4OaaVZ8lIY5+oNqA/BXXgLyXv0+5wHP68R79hg==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-custom-media/8.0.2_postcss@8.4.13:
    resolution: {integrity: sha512-7yi25vDAoHAkbhAzX9dHx2yc6ntS4jQvejrNcC+csQJAXjj15e7VcWfMgLqBNAbOvqi5uIa9huOVwdHbf+sKqg==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-custom-properties/12.1.9_postcss@8.4.13:
    resolution: {integrity: sha512-/E7PRvK8DAVljBbeWrcEQJPG72jaImxF3vvCNFwv9cC8CzigVoNIpeyfnJzphnN3Fd8/auBf5wvkw6W9MfmTyg==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-custom-selectors/6.0.3_postcss@8.4.13:
    resolution: {integrity: sha512-fgVkmyiWDwmD3JbpCmB45SvvlCD6z9CG6Ie6Iere22W5aHea6oWa7EM2bpnv2Fj3I94L3VbtvX9KqwSi5aFzSg==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /postcss-dir-pseudo-class/6.0.5_postcss@8.4.13:
    resolution: {integrity: sha512-eqn4m70P031PF7ZQIvSgy9RSJ5uI2171O/OO/zcRNYpJbvaeKFUlar1aJ7rmgiQtbm0FSPsRewjpdS0Oew7MPA==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /postcss-double-position-gradients/3.1.2_postcss@8.4.13:
    resolution: {integrity: sha512-GX+FuE/uBR6eskOK+4vkXgT6pDkexLokPaz/AbJna9s5Kzp/yl488pKPjhy0obB475ovfT1Wv8ho7U/cHNaRgQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0_postcss@8.4.13
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-env-function/4.0.6_postcss@8.4.13:
    resolution: {integrity: sha512-kpA6FsLra+NqcFnL81TnsU+Z7orGtDTxcOhl6pwXeEq1yFPpRMkCDpHhrz8CFQDr/Wfm0jLiNQ1OsGGPjlqPwA==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-focus-visible/6.0.4_postcss@8.4.13:
    resolution: {integrity: sha512-QcKuUU/dgNsstIK6HELFRT5Y3lbrMLEOwG+A4s5cA+fx3A3y/JTq3X9LaOj3OC3ALH0XqyrgQIgey/MIZ8Wczw==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /postcss-focus-within/5.0.4_postcss@8.4.13:
    resolution: {integrity: sha512-vvjDN++C0mu8jz4af5d52CB184ogg/sSxAFS+oUJQq2SuCe7T5U2iIsVJtsCp2d6R4j0jr5+q3rPkBVZkXD9fQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /postcss-font-variant/5.0.0_postcss@8.4.13:
    resolution: {integrity: sha512-1fmkBaCALD72CK2a9i468mA/+tr9/1cBxRRMXOUaZqO43oWPR5imcyPjXwuv7PXbCid4ndlP5zWhidQVVa3hmA==, registry: https://registry.npm.taobao.org/}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.13
    dev: true

  /postcss-gap-properties/3.0.5_postcss@8.4.13:
    resolution: {integrity: sha512-IuE6gKSdoUNcvkGIqdtjtcMtZIFyXZhmFd5RUlg97iVEvp1BZKV5ngsAjCjrVy+14uhGBQl9tzmi1Qwq4kqVOg==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
    dev: true

  /postcss-image-set-function/4.0.7_postcss@8.4.13:
    resolution: {integrity: sha512-9T2r9rsvYzm5ndsBE8WgtrMlIT7VbtTfE7b3BQnudUqnBcBo7L758oc+o+pdj/dUV0l5wjwSdjeOH2DZtfv8qw==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-initial/4.0.1_postcss@8.4.13:
    resolution: {integrity: sha512-0ueD7rPqX8Pn1xJIjay0AZeIuDoF+V+VvMt/uOnn+4ezUKhZM/NokDeP6DwMNyIoYByuN/94IQnt5FEkaN59xQ==, registry: https://registry.npm.taobao.org/}
    peerDependencies:
      postcss: ^8.0.0
    dependencies:
      postcss: 8.4.13
    dev: true

  /postcss-lab-function/4.2.1_postcss@8.4.13:
    resolution: {integrity: sha512-xuXll4isR03CrQsmxyz92LJB2xX9n+pZJ5jE9JgcnmsCammLyKdlzrBin+25dy6wIjfhJpKBAN80gsTlCgRk2w==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0_postcss@8.4.13
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-logical/5.0.4_postcss@8.4.13:
    resolution: {integrity: sha512-RHXxplCeLh9VjinvMrZONq7im4wjWGlRJAqmAVLXyZaXwfDWP73/oq4NdIp+OZwhQUMj0zjqDfM5Fj7qby+B4g==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.13
    dev: true

  /postcss-media-minmax/5.0.0_postcss@8.4.13:
    resolution: {integrity: sha512-yDUvFf9QdFZTuCUg0g0uNSHVlJ5X1lSzDZjPSFaiCWvjgsvu8vEVxtahPrLMinIDEEGnx6cBe6iqdx5YWz08wQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.13
    dev: true

  /postcss-nesting/10.2.0_postcss@8.4.13:
    resolution: {integrity: sha512-EwMkYchxiDiKUhlJGzWsD9b2zvq/r2SSubcRrgP+jujMXFzqvANLt16lJANC+5uZ6hjI7lpRmI6O8JIl+8l1KA==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/selector-specificity': 2.0.2_qiplrb533afbljv7sbepwo7yse
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /postcss-opacity-percentage/1.1.2:
    resolution: {integrity: sha512-lyUfF7miG+yewZ8EAk9XUBIlrHyUE6fijnesuz+Mj5zrIHIEw6KcIZSOk/elVMqzLvREmXB83Zi/5QpNRYd47w==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    dev: true

  /postcss-overflow-shorthand/3.0.4_postcss@8.4.13:
    resolution: {integrity: sha512-otYl/ylHK8Y9bcBnPLo3foYFLL6a6Ak+3EQBPOTR7luMYCOsiVTUk1iLvNf6tVPNGXcoL9Hoz37kpfriRIFb4A==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-page-break/3.0.4_postcss@8.4.13:
    resolution: {integrity: sha512-1JGu8oCjVXLa9q9rFTo4MbeeA5FMe00/9C7lN4va606Rdb+HkxXtXsmEDrIraQ11fGz/WvKWa8gMuCKkrXpTsQ==, registry: https://registry.npm.taobao.org/}
    peerDependencies:
      postcss: ^8
    dependencies:
      postcss: 8.4.13
    dev: true

  /postcss-place/7.0.5_postcss@8.4.13:
    resolution: {integrity: sha512-wR8igaZROA6Z4pv0d+bvVrvGY4GVHihBCBQieXFY3kuSuMyOmEnnfFzHl/tQuqHZkfkIVBEbDvYcFfHmpSet9g==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-preset-env/7.8.2_postcss@8.4.13:
    resolution: {integrity: sha512-rSMUEaOCnovKnwc5LvBDHUDzpGP+nrUeWZGWt9M72fBvckCi45JmnJigUr4QG4zZeOHmOCNCZnd2LKDvP++ZuQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      '@csstools/postcss-cascade-layers': 1.1.1_postcss@8.4.13
      '@csstools/postcss-color-function': 1.1.1_postcss@8.4.13
      '@csstools/postcss-font-format-keywords': 1.0.1_postcss@8.4.13
      '@csstools/postcss-hwb-function': 1.0.2_postcss@8.4.13
      '@csstools/postcss-ic-unit': 1.0.1_postcss@8.4.13
      '@csstools/postcss-is-pseudo-class': 2.0.7_postcss@8.4.13
      '@csstools/postcss-nested-calc': 1.0.0_postcss@8.4.13
      '@csstools/postcss-normalize-display-values': 1.0.1_postcss@8.4.13
      '@csstools/postcss-oklab-function': 1.1.1_postcss@8.4.13
      '@csstools/postcss-progressive-custom-properties': 1.3.0_postcss@8.4.13
      '@csstools/postcss-stepped-value-functions': 1.0.1_postcss@8.4.13
      '@csstools/postcss-text-decoration-shorthand': 1.0.0_postcss@8.4.13
      '@csstools/postcss-trigonometric-functions': 1.0.2_postcss@8.4.13
      '@csstools/postcss-unset-value': 1.0.2_postcss@8.4.13
      autoprefixer: 10.4.12_postcss@8.4.13
      browserslist: 4.21.4
      css-blank-pseudo: 3.0.3_postcss@8.4.13
      css-has-pseudo: 3.0.4_postcss@8.4.13
      css-prefers-color-scheme: 6.0.3_postcss@8.4.13
      cssdb: 7.0.1
      postcss: 8.4.13
      postcss-attribute-case-insensitive: 5.0.2_postcss@8.4.13
      postcss-clamp: 4.1.0_postcss@8.4.13
      postcss-color-functional-notation: 4.2.4_postcss@8.4.13
      postcss-color-hex-alpha: 8.0.4_postcss@8.4.13
      postcss-color-rebeccapurple: 7.1.1_postcss@8.4.13
      postcss-custom-media: 8.0.2_postcss@8.4.13
      postcss-custom-properties: 12.1.9_postcss@8.4.13
      postcss-custom-selectors: 6.0.3_postcss@8.4.13
      postcss-dir-pseudo-class: 6.0.5_postcss@8.4.13
      postcss-double-position-gradients: 3.1.2_postcss@8.4.13
      postcss-env-function: 4.0.6_postcss@8.4.13
      postcss-focus-visible: 6.0.4_postcss@8.4.13
      postcss-focus-within: 5.0.4_postcss@8.4.13
      postcss-font-variant: 5.0.0_postcss@8.4.13
      postcss-gap-properties: 3.0.5_postcss@8.4.13
      postcss-image-set-function: 4.0.7_postcss@8.4.13
      postcss-initial: 4.0.1_postcss@8.4.13
      postcss-lab-function: 4.2.1_postcss@8.4.13
      postcss-logical: 5.0.4_postcss@8.4.13
      postcss-media-minmax: 5.0.0_postcss@8.4.13
      postcss-nesting: 10.2.0_postcss@8.4.13
      postcss-opacity-percentage: 1.1.2
      postcss-overflow-shorthand: 3.0.4_postcss@8.4.13
      postcss-page-break: 3.0.4_postcss@8.4.13
      postcss-place: 7.0.5_postcss@8.4.13
      postcss-pseudo-class-any-link: 7.1.6_postcss@8.4.13
      postcss-replace-overflow-wrap: 4.0.0_postcss@8.4.13
      postcss-selector-not: 6.0.1_postcss@8.4.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-pseudo-class-any-link/7.1.6_postcss@8.4.13:
    resolution: {integrity: sha512-9sCtZkO6f/5ML9WcTLcIyV1yz9D1rf0tWc+ulKcvV30s0iZKS/ONyETvoWsr6vnrmW+X+KmuK3gV/w5EWnT37w==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /postcss-px-to-viewport/1.1.1:
    resolution: {integrity: sha512-2x9oGnBms+e0cYtBJOZdlwrFg/mLR4P1g2IFu7jYKvnqnH/HLhoKyareW2Q/x4sg0BgklHlP1qeWo2oCyPm8FQ==, registry: https://registry.npm.taobao.org/}
    dependencies:
      object-assign: 4.1.1
      postcss: 8.4.16
    dev: true

  /postcss-replace-overflow-wrap/4.0.0_postcss@8.4.13:
    resolution: {integrity: sha512-KmF7SBPphT4gPPcKZc7aDkweHiKEEO8cla/GjcBK+ckKxiZslIu3C4GCRW3DNfL0o7yW7kMQu9xlZ1kXRXLXtw==, registry: https://registry.npm.taobao.org/}
    peerDependencies:
      postcss: ^8.0.3
    dependencies:
      postcss: 8.4.13
    dev: true

  /postcss-selector-not/6.0.1_postcss@8.4.13:
    resolution: {integrity: sha512-1i9affjAe9xu/y9uqWH+tD4r6/hDaXJruk8xn2x1vzxC2U3J3LKO3zJW4CyxlNhA56pADJ/djpEwpH1RClI2rQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.13
      postcss-selector-parser: 6.0.10
    dev: true

  /postcss-selector-parser/6.0.10:
    resolution: {integrity: sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-value-parser/4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==, registry: https://registry.npm.taobao.org/}
    dev: true

  /postcss/8.4.13:
    resolution: {integrity: sha512-jtL6eTBrza5MPzy8oJLFuUscHDXTV5KcLlqAWHl5q5WYRfnNRGSmOZmOZ1T6Gy7A99mOZfqungmZMpMmCVJ8ZA==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.4
      picocolors: 1.0.0
      source-map-js: 1.0.2
    dev: true

  /postcss/8.4.16:
    resolution: {integrity: sha512-ipHE1XBvKzm5xI7hiHCZJCSugxvsdq2mPnsq5+UF+VHCjiBvtDrlxJfMBToWaP9D5XlgNmcFGqoHmUn0EYEaRQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.4
      picocolors: 1.0.0
      source-map-js: 1.0.2

  /prr/1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==, registry: https://registry.npm.taobao.org/}
    dev: true
    optional: true

  /queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==, registry: https://registry.npm.taobao.org/}
    dev: true

  /readdirp/3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1
    dev: true

  /resolve/1.22.1:
    resolution: {integrity: sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==}
    hasBin: true
    dependencies:
      is-core-module: 2.10.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /reusify/1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==, registry: https://registry.npm.taobao.org/}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rollup/2.78.1:
    resolution: {integrity: sha512-VeeCgtGi4P+o9hIg+xz4qQpRl6R401LWEXBmxYKOV4zlF82lyhgh2hTZnheFUbANE8l2A41F458iwj2vEYaXJg==}
    engines: {node: '>=10.0.0'}
    hasBin: true
    optionalDependencies:
      fsevents: 2.3.2
    dev: true

  /run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==, registry: https://registry.npm.taobao.org/}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /safer-buffer/2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==, registry: https://registry.npm.taobao.org/}
    dev: true
    optional: true

  /sax/1.2.4:
    resolution: {integrity: sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==, registry: https://registry.npm.taobao.org/}
    dev: true
    optional: true

  /semver/5.7.1:
    resolution: {integrity: sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==, registry: https://registry.npm.taobao.org/}
    hasBin: true
    dev: true
    optional: true

  /semver/7.3.7:
    resolution: {integrity: sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /sentence-case/3.0.4:
    resolution: {integrity: sha512-8LS0JInaQMCRoQ7YUytAo/xUu5W2XnQxV2HI/6uM6U7CITS1RqPElr30V6uIqyMKM9lJGRVFy5/4CuzcixNYSg==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.4.0
      upper-case-first: 2.0.2
    dev: true

  /snake-case/3.0.4:
    resolution: {integrity: sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.4.0
    dev: true

  /source-map-js/1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=0.10.0'}

  /source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  /sourcemap-codec/1.4.8:
    resolution: {integrity: sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==, registry: https://registry.npm.taobao.org/}

  /supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}
    dev: true

  /throttle-debounce/5.0.0:
    resolution: {integrity: sha512-2iQTSgkkc1Zyk0MeVrt/3BvuOXYPl/R8Z0U2xxo9rjwNciaHDG3R+Lm6dh4EeUci49DanvBnuqI6jshoQQRGEg==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=12.22'}
    dev: false

  /to-fast-properties/2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=4'}

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: true

  /tslib/2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==, registry: https://registry.npm.taobao.org/}
    dev: false

  /tslib/2.4.0:
    resolution: {integrity: sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==, registry: https://registry.npm.taobao.org/}
    dev: true

  /typescript/4.8.3:
    resolution: {integrity: sha512-goMHfm00nWPa8UvR/CPSvykqf6dVV8x/dp0c5mFTMTIu0u0FlGWRioyy7Nn0PGAdHxpJZnuO/ut+PpQ8UiHAig==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=4.2.0'}
    hasBin: true
    dev: true

  /universalify/2.0.0:
    resolution: {integrity: sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==}
    engines: {node: '>= 10.0.0'}
    dev: true

  /unplugin-vue-components/0.22.7_vue@3.2.39:
    resolution: {integrity: sha512-MJEAKJF9bRykTRvJ4WXF0FNMAyt1PX3iwpu2NN+li35sAKjQv6sC1col5aZDLiwDZDo2AGwxNkzLJFKaun9qHw==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/parser': ^7.15.8
      vue: 2 || 3
    peerDependenciesMeta:
      '@babel/parser':
        optional: true
    dependencies:
      '@antfu/utils': 0.5.2
      '@rollup/pluginutils': 4.2.1
      chokidar: 3.5.3
      debug: 4.3.4
      fast-glob: 3.2.12
      local-pkg: 0.4.2
      magic-string: 0.26.4
      minimatch: 5.1.0
      resolve: 1.22.1
      unplugin: 0.9.6
      vue: 3.2.39
    transitivePeerDependencies:
      - supports-color
    dev: true

  /unplugin-vue-define-options/0.10.0_vue@3.2.39:
    resolution: {integrity: sha512-YvFdhdakRfrtArtMRCnqiQ74zYP/KicZpAqUYqPhtAL1r6aBxpj+soWBg0k5VxCbmwvjkuEWlYgOgdf6hqOc/A==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=14.19.0'}
    dependencies:
      '@rollup/pluginutils': 4.2.1
      '@vue-macros/common': 0.10.0_vue@3.2.39
      ast-walker-scope: 0.2.3
      unplugin: 0.9.6
    transitivePeerDependencies:
      - vue
    dev: true

  /unplugin/0.9.6:
    resolution: {integrity: sha512-YYLtfoNiie/lxswy1GOsKXgnLJTE27la/PeCGznSItk+8METYZErO+zzV9KQ/hXhPwzIJsfJ4s0m1Rl7ZCWZ4Q==, registry: https://registry.npm.taobao.org/}
    dependencies:
      acorn: 8.8.0
      chokidar: 3.5.3
      webpack-sources: 3.2.3
      webpack-virtual-modules: 0.4.5
    dev: true

  /update-browserslist-db/1.0.9_browserslist@4.21.4:
    resolution: {integrity: sha512-/xsqn21EGVdXI3EXSum1Yckj3ZVZugqyOZQ/CxYPBD/R+ko9NSUScf8tFF4dOKY+2pvSSJA/S+5B8s4Zr4kyvg==, registry: https://registry.npm.taobao.org/}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.21.4
      escalade: 3.1.1
      picocolors: 1.0.0
    dev: true

  /upper-case-first/2.0.2:
    resolution: {integrity: sha512-514ppYHBaKwfJRK/pNC6c/OxfGa0obSnAl106u97Ed0I625Nin96KAjttZF6ZL3e1XLtphxnqrOi9iWgm+u+bg==}
    dependencies:
      tslib: 2.4.0
    dev: true

  /upper-case/2.0.2:
    resolution: {integrity: sha512-KgdgDGJt2TpuwBUIjgG6lzw2GWFRCW9Qkfkiv0DxqHHLYJHmtmdUIKcZd8rHgFSjopVTlw6ggzCm1b8MFQwikg==}
    dependencies:
      tslib: 2.4.0
    dev: true

  /util-deprecate/1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==, registry: https://registry.npm.taobao.org/}
    dev: true

  /vite-plugin-compression/0.5.1_vite@3.1.1:
    resolution: {integrity: sha512-5QJKBDc+gNYVqL/skgFAP81Yuzo9R+EAf19d+EtsMF/i8kFUpNi3J/H01QD3Oo8zBQn+NzoCIFkpPLynoOzaJg==}
    peerDependencies:
      vite: '>=2.0.0'
    dependencies:
      chalk: 4.1.2
      debug: 4.3.4
      fs-extra: 10.1.0
      vite: 3.1.1_less@4.1.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vite-plugin-style-import/2.0.0_vite@3.1.1:
    resolution: {integrity: sha512-qtoHQae5dSUQPo/rYz/8p190VU5y19rtBaeV7ryLa/AYAU/e9CG89NrN/3+k7MR8mJy/GPIu91iJ3zk9foUOSA==}
    peerDependencies:
      vite: '>=2.0.0'
    dependencies:
      '@rollup/pluginutils': 4.2.1
      change-case: 4.1.2
      console: 0.7.2
      es-module-lexer: 0.9.3
      fs-extra: 10.1.0
      magic-string: 0.25.9
      pathe: 0.2.0
      vite: 3.1.1_less@4.1.3
    dev: true

  /vite/3.1.1_less@4.1.3:
    resolution: {integrity: sha512-hgxQWev/AL7nWYrqByYo8nfcH9n97v6oFsta9+JX8h6cEkni7nHKP2kJleNYV2kcGhE8jsbaY1aStwPZXzPbgA==}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      less: '*'
      sass: '*'
      stylus: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      less:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      terser:
        optional: true
    dependencies:
      esbuild: 0.15.9
      less: 4.1.3
      postcss: 8.4.16
      resolve: 1.22.1
      rollup: 2.78.1
    optionalDependencies:
      fsevents: 2.3.2
    dev: true

  /vue-tsc/0.40.13_typescript@4.8.3:
    resolution: {integrity: sha512-xzuN3g5PnKfJcNrLv4+mAjteMd5wLm5fRhW0034OfNJZY4WhB07vhngea/XeGn7wNYt16r7syonzvW/54dcNiA==, registry: https://registry.npm.taobao.org/}
    hasBin: true
    peerDependencies:
      typescript: '*'
    dependencies:
      '@volar/vue-language-core': 0.40.13
      '@volar/vue-typescript': 0.40.13
      typescript: 4.8.3
    dev: true

  /vue/3.2.39:
    resolution: {integrity: sha512-tRkguhRTw9NmIPXhzk21YFBqXHT2t+6C6wPOgQ50fcFVWnPdetmRqbmySRHznrYjX2E47u0cGlKGcxKZJ38R/g==, registry: https://registry.npm.taobao.org/}
    dependencies:
      '@vue/compiler-dom': 3.2.39
      '@vue/compiler-sfc': 3.2.39
      '@vue/runtime-dom': 3.2.39
      '@vue/server-renderer': 3.2.39_vue@3.2.39
      '@vue/shared': 3.2.39

  /vue3-seamless-scroll/2.0.1:
    resolution: {integrity: sha512-mI3BaDU3pjcPUhVSw3/xNKdfPBDABTi/OdZaZqKysx4cSdNfGRbVvGNDzzptBbJ5S7imv5T55l6x/SqgnxKreg==, registry: https://registry.npm.taobao.org/}
    dependencies:
      throttle-debounce: 5.0.0
    dev: false

  /webpack-sources/3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==, registry: https://registry.npm.taobao.org/}
    engines: {node: '>=10.13.0'}
    dev: true

  /webpack-virtual-modules/0.4.5:
    resolution: {integrity: sha512-8bWq0Iluiv9lVf9YaqWQ9+liNgXSHICm+rg544yRgGYaR8yXZTVBaHZkINZSB2yZSWo4b0F6MIxqJezVfOEAlg==, registry: https://registry.npm.taobao.org/}
    dev: true

  /yallist/4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==, registry: https://registry.npm.taobao.org/}
    dev: true

  /zrender/5.3.2:
    resolution: {integrity: sha512-8IiYdfwHj2rx0UeIGZGGU4WEVSDEdeVCaIg/fomejg1Xu6OifAL1GVzIPHg2D+MyUkbNgPWji90t0a8IDk+39w==, registry: https://registry.npm.taobao.org/}
    dependencies:
      tslib: 2.3.0
    dev: false

  registry.npmmirror.com/@types/lodash/4.14.186:
    resolution: {integrity: sha512-eHcVlLXP0c2FlMPm56ITode2AgLMSa6aJ05JTTbYbI+7EMkCEE5qk2E41d5g2lCVTqRe0GnnRFurmlCsDODrPw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.186.tgz}
    name: '@types/lodash'
    version: 4.14.186
    dev: true

  registry.npmmirror.com/dayjs/1.11.5:
    resolution: {integrity: sha512-CAdX5Q3YW3Gclyo5Vpqkgpj8fSdLQcRuzfX6mC6Phy0nfJ0eGYOeS7m4mt2plDWLAtA4TqTakvbboHvUxfe4iA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dayjs/-/dayjs-1.11.5.tgz}
    name: dayjs
    version: 1.11.5
    dev: false

  registry.npmmirror.com/lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz}
    name: lodash
    version: 4.17.21
    dev: false
